/**
 * Run `npm run dev` in your terminal to start a development server
 * Open a browser tab at http://localhost:8787/ to see your worker in action
 * Run `npm run deploy` to publish your worker
 *
 * Bind resources to your worker in `wrangler.toml`.
 *
 * Learn more at https://developers.cloudflare.com/workers/
 */

import { fromHono } from 'chanfana';
import { Context, Hono, Next } from 'hono';
import { cors } from 'hono/cors';
// import { upgradeWebSocket } from 'hono/cloudflare-workers';
import { ExchangeToken } from './endpoints/exchange-token';
import { GenApiKey, GetApiKey, UpdateApiKey } from './endpoints/api-key';
import { ImageUpscaler } from './endpoints/image-upscaler';
// import { TaskInfoDurableObject } from './durableObjects';
import { FetchTask, FetchTaskSSE } from './endpoints/fetch-task-sse';
import { authMiddleware } from './middlewares/auth';
import { creditEnoughMiddleware } from './middlewares/credit';
import { FileUpload } from './endpoints/file-upload';
import { RemoveBg } from './endpoints/remove-bg';
import {
	GetCredit,
	GetAppCredit,
} from './endpoints/credit';
import { APP, Bindings } from './types';
import { SpeedPainter } from './endpoints/speedpainter';
import { VideoUpscaler } from './endpoints/video-upscaler';
import { TextBehindImage } from './endpoints/text-behind-image';
import { StripeAddPayAsGoCredit, StripeResetPlanCredit } from './endpoints/stripe-credit';
import { FileUploadBase64 } from './endpoints/file-upload-base64';
import { handleTaskQueue } from './queue';
import { handleScheduled } from './scheduled';
import { GetUserInfo } from './endpoints/get-user-info';
import { CancelTask } from './endpoints/cancel-task';
import { ImageVectorization } from './endpoints/image-vectorization';
import { ImageExtends } from './endpoints/image-extends';
// import { TaskDataAnalysis } from './endpoints/task-data-analysis';
import { ImageProxy } from './endpoints/image-proxy';
import { GeminiImageGeneration } from './endpoints/gemini-image-generation';
import { ImageGenerator } from './endpoints/image-generator';
import { Chat2Design } from './endpoints/chat-2-design';
import { GetAppTemplate } from './endpoints/get-app-template';
import { DeleteTask } from './endpoints/delete-task';
import { GetUserTasks } from './endpoints/get-user-tasks';
import { Proxy302 } from './endpoints/proxy-302';
import { CalculateCredits } from './endpoints/calculate-credits';
import { ImageRelighter } from './endpoints/image-relighter';
import { PosthogProxy } from './endpoints/posthog-proxy';


// Star a Hono app
const app = new Hono();

app.use(
	'/api/*',
	cors({
		credentials: true,
		origin: '*',
	}),
);

// Register WebSocket events
//app.get('/api/task/:id/ws', durableObjectMiddleware, upgradeWebSocket(createEvents));

// Setup OpenAPI registry
const options = {
	docs_url: '/docs',
	schema: {
		info: {
			title: 'A1d Auth Worker API',
			version: '1.0',
		},
		servers: [
			{
				url: '/',
				description: 'Development server',
			},
			{
				url: 'https://auth-cf-testk-xshar.workers.dev/',
				description: 'Production server',
			},
		],
		security: [
			{
				BearerAuth: [],
			},
			{
				KeyAuth: [],
			},
		],
	},
} as const;
const openapi = fromHono<{ Bindings: Bindings }>(app, options);

openapi.registry.registerComponent('securitySchemes', 'BearerAuth', {
	type: 'http',
	scheme: 'bearer',
	bearerFormat: 'JWT',
	description: 'JWT token with Bearer prefix',
});

openapi.registry.registerComponent('securitySchemes', 'KeyAuth', {
	type: 'apiKey',
	in: 'header',
	name: 'Authorization',
	description: 'API Key with KEY prefix (e.g., KEY your-api-key)',
});

// 修改中间件工厂函数
const createAppNameMiddleware = (app: string) => {
	return async (c: Context<{ Bindings: Bindings }>, next: Next) => {
		// 获取现有的 payload
		const existingPayload = c.get('jwtPayload') || {};

		// 从请求体中获取 source
		const body = await c.req.json();
		// 合并新的 app 信息和 source 到现有的 payload
		c.set('jwtPayload', {
			...existingPayload,
			app,
		});

		c.set('requestBody', body);

		await next();
	};
};

openapi.post('/api/exchange-token', ExchangeToken);
openapi.post('/api/gen-api-key', authMiddleware, GenApiKey);
openapi.get('/api/get-api-key', authMiddleware, GetApiKey);
openapi.put('/api/update-api-key', authMiddleware, UpdateApiKey);

// Stripe webhook endpoints (no auth middleware)
openapi.post('/api/stripe/add-pay-as-you-go', StripeAddPayAsGoCredit);
openapi.post('/api/stripe/reset-plan-credits', StripeResetPlanCredit);

// get user info
openapi.get('/api/get-user-info', authMiddleware, GetUserInfo);

// iu

openapi.post('/api/image-upscaler', authMiddleware, createAppNameMiddleware(APP.IMAGE_UPSCALER), creditEnoughMiddleware, ImageUpscaler);

// task
openapi.get('/api/task/:taskId/sse', FetchTaskSSE);
openapi.get('/api/task/:taskId', FetchTask);
openapi.post('/api/uploads/:file_name', authMiddleware, FileUpload);
openapi.post('/api/file-upload-base64', authMiddleware, FileUploadBase64);
// export { TaskInfoDurableObject };

// remove-bg

openapi.post('/api/remove-bg', authMiddleware, createAppNameMiddleware(APP.REMOVE_BG), creditEnoughMiddleware, RemoveBg);

// speed painter
openapi.post('/api/speedpainter', authMiddleware, createAppNameMiddleware(APP.SPEEDPAINTER), creditEnoughMiddleware, SpeedPainter);

// video upscaler
openapi.post('/api/video-upscaler', authMiddleware, createAppNameMiddleware(APP.VIDEO_UPSCALER), creditEnoughMiddleware, VideoUpscaler);

// image vectorization
openapi.post('/api/image-vectorization', authMiddleware, createAppNameMiddleware(APP.IMAGE_VECTORIZATION), creditEnoughMiddleware, ImageVectorization);

// image extends 
openapi.post('/api/image-extends', authMiddleware, createAppNameMiddleware(APP.IMAGE_EXTENDS), creditEnoughMiddleware, ImageExtends);

// image relighter
openapi.post('/api/image-relighter', authMiddleware, createAppNameMiddleware(APP.IMAGE_RELIGHTER), creditEnoughMiddleware, ImageRelighter);

// credit
openapi.get('/api/get-credit', authMiddleware, GetCredit);

// Add this line with other route registrations
openapi.post('/api/task/:taskId/cancel', authMiddleware, CancelTask);

// text behind image
openapi.post('/api/text-behind-image', authMiddleware, createAppNameMiddleware(APP.TEXT_BEHIND_IMAGE), creditEnoughMiddleware, TextBehindImage);

// openapi.post('/api/data/task-data-analysis', authMiddleware, TaskDataAnalysis);
// image generator
openapi.post('/api/image-generator', authMiddleware, createAppNameMiddleware(APP.IMAGE_GENERATOR), creditEnoughMiddleware, ImageGenerator);
// image proxy - publicly accessible without authentication
openapi.get('/api/image-proxy', ImageProxy);

// Gemini image generation
openapi.post('/api/gemini-image-generation', authMiddleware, GeminiImageGeneration);

// Chat 2 Design
openapi.post('/api/chat-2-design', authMiddleware, createAppNameMiddleware(APP.CHAT_2_DESIGN), creditEnoughMiddleware, Chat2Design);


// App templates endpoint - get templates based on app parameter
openapi.get('/api/templates', GetAppTemplate);

// Add new task endpoints
openapi.delete('/api/task/:taskId', authMiddleware, DeleteTask);
openapi.get('/api/tasks', authMiddleware, GetUserTasks);
openapi.get('/api/app-credit', GetAppCredit);

// Calculate credits endpoint - no auth required for calculation
openapi.post('/api/calculate-credits', CalculateCredits);

// 302 API proxy
openapi.post('/api/302', authMiddleware, createAppNameMiddleware(APP.AI_302), creditEnoughMiddleware, Proxy302);
openapi.get('/api/302', authMiddleware, Proxy302);

// PostHog Analytics proxy - no auth required
openapi.all('/ph/*', PosthogProxy);

// Export the Hono app
export default {
    fetch: app.fetch,
    queue: handleTaskQueue,
    scheduled: handleScheduled
};
