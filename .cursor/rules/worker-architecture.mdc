---
description: 
globs: 
alwaysApply: true
---
# Worker Architecture

This project consists of two main Cloudflare Worker services:

## Service Structure
- **api-service**: Core API functionality for processing various tasks
- **auth-service**: Authentication service for token management and API keys

## Common Patterns

Both services follow these architectural patterns:
- Hono-based API routing with OpenAPI documentation
- JWT and API key authentication
- Endpoints organized by feature

## API Service

The API service handles all core functionality with the following structure:

```mermaid
graph TB
    subgraph Client
        C[Client Applications]
    end

    subgraph CloudflareWorker["Cloudflare Worker (api-service)"]
        Router[API Router]
        
        subgraph Middlewares
            Auth[Auth Middleware]
            Credit[Credit Check]
            CORS[CORS Handler]
        end
        
        subgraph CoreServices["Core Services"]
            TaskQueue[Task Queue Handler]
            Schedule[Scheduled Tasks]
            FileProcess[File Processing]
        end
        
        subgraph Endpoints["API Endpoints"]
            Auth_EP[Authentication APIs]
            File_EP[File Processing APIs]
            Credit_EP[Credit Management]
            Task_EP[Task Management]
        end
    end

    subgraph Infrastructure["Infrastructure"]
        DO[Durable Objects]
        KV[KV Storage]
        Queue[Queue Service]
        Supabase[(Supabase DB)]
    end

    subgraph ExternalServices["External Services"]
        Stripe[Stripe Payment]
        AI[AI Services]
    end

    C -->|HTTP/WebSocket| Router
    Router --> Middlewares
    Middlewares --> Endpoints
    Endpoints --> CoreServices
    CoreServices --> Infrastructure
    Credit_EP --> Stripe
    FileProcess --> AI
    TaskQueue --> Queue
    CoreServices --> Supabase
```

### Key Components:
- **Entry Point**: `src/index.ts` - Defines routes and initializes the app
- **Endpoints**: `/src/endpoints/*` - API endpoint handlers organized by feature
- **Middlewares**: `/src/middlewares/*` - Auth, credit checks, etc.
- **Services**: `/src/service/*` - Core business logic 
- **Models**: `/src/model/*` - Data models
- **Utils**: `/src/utils/*` - Utility functions
- **Types**: `/src/types/*` - TypeScript type definitions

## Auth Service

The Auth service provides authentication functionality:

- **Token Exchange**: Convert third-party tokens to A1D tokens 
- **API Key Management**: Generate and manage API keys

### Key Components:
- **Entry Point**: `src/index.ts` - Defines routes and initializes the app
- **Endpoints**: Handles token exchange and API key management
- **Middlewares**: Authentication validation
- **Services**: Core authentication logic

## Infrastructure

Both services use:
- **Durable Objects**: State management
- **KV Storage**: Key-value data storage
- **Queue Service**: Async task processing
- **Supabase**: Primary database

## Development Workflow
- Configuration is managed through `wrangler.toml` files
- Local development with `npm run dev`
- Deployment with `npm run deploy`
