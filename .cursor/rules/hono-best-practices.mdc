---
description: 
globs: 
alwaysApply: true
---
# Hono Best Practices

## Project Structure

- **Route Organization**: Define routes in `src/index.ts` and use the OpenAPI registry for documentation
- **Endpoint Handlers**: Place endpoint handlers in `src/endpoints/` directory, one file per feature
- **Middleware**: Store middleware functions in `src/middlewares/` directory
- **Services**: Keep business logic in `src/service/` directory
- **Types**: Define TypeScript types in `src/types/` or `src/types.ts`

## API Design

- Use OpenAPI registry via `chanfana` to document endpoints:

```typescript
const openapi = fromHono(app, options);

openapi.registry.registerComponent('securitySchemes', 'BearerAuth', {
  type: 'http',
  scheme: 'bearer',
  bearerFormat: 'JWT',
});

// Register routes with openapi, not directly on app
openapi.post('/api/endpoint', handlerFunction);
```

## Middleware

- Create middleware for cross-cutting concerns:
  - Authentication via `authMiddleware`
  - Resource validation via `creditEnoughMiddleware`
  - CORS handling 

- Apply middleware at the route or group level:

```typescript
app.use(
  '/api/*',
  cors({
    credentials: true,
    origin: '*',
  }),
);

// Or apply to specific routes
openapi.post('/api/endpoint', authMiddleware, handlerFunction);
```

## Request/Response Handling

- Use Hono's context object to handle requests and responses:

```typescript
// Request handling
const body = await c.req.json();
const params = c.req.param();
const query = c.req.query();

// Response handling
return c.json({ success: true, data: result });
```

- For structured responses, follow this pattern:

```typescript
return c.json({
  success: true,
  data: {
    // Response data here
  }
});
```

## Error Handling

- Use consistent error response format:

```typescript
return c.json({
  success: false,
  error: {
    message: 'Error message',
    code: 'ERROR_CODE'
  }
}, 400);
```

- Handle async errors with try/catch blocks:

```typescript
try {
  // Code that might throw
} catch (error) {
  console.error(error);
  return c.json({
    success: false,
    error: {
      message: error.message || 'Internal server error',
      code: 'INTERNAL_ERROR'
    }
  }, 500);
}
```

## TypeScript Integration

- Define request/response types with Zod or TypeScript interfaces
- Use generics with Hono for type safety:

```typescript
const app = new Hono<{ Bindings: Bindings }>();
```

- Type Bindings to include environment variables and KV stores:

```typescript
export interface Bindings {
  JWT_SECRET: string;
  KV_STORE: KVNamespace;
  // Other bindings
}
``` 