---
description: 
globs: 
alwaysApply: true
---
# TypeScript Best Practices

## Type Definitions

- Define interfaces and types in `src/types/` directory or `src/types.ts`
- Export type definitions to reuse across the codebase:

```typescript
// Example type definition for Bindings
export interface Bindings {
  JWT_SECRET: string;
  SUPABASE_URL: string;
  SUPABASE_SERVICE_ROLE_KEY: string;
  // Add other environment variables
}

// Example for API responses
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code: string;
  };
}
```

## Type Safety

- Use generics for reusable components and functions:

```typescript
// Generic function example
async function fetchData<T>(url: string): Promise<ApiResponse<T>> {
  // Implementation
}
```

- Use type predicates for type narrowing:

```typescript
function isErrorResponse(response: any): response is ErrorResponse {
  return 'error' in response;
}
```

## Enums and Constants

- Use string literal union types for limited sets of values:

```typescript
// Better than enums in many cases
export type TaskStatus = 'WAITING' | 'PROCESSING' | 'SUCCESS' | 'FINISHED' | 'FAILED' | 'CANCEL';
```

- Create constants for fixed values:

```typescript
export const APP = {
  IMAGE_UPSCALER: 'image-upscaler',
  REMOVE_BG: 'remove-bg',
  SPEEDPAINTER: 'speedpainter',
  // Other app types
} as const;
```

## Type Guards

- Use type guards to ensure type safety at runtime:

```typescript
function isValidToken(token: unknown): token is string {
  return typeof token === 'string' && token.length > 0;
}

// Usage
if (!isValidToken(token)) {
  return c.json({ success: false, error: { message: 'Invalid token' } }, 400);
}
```

## Async/Await

- Use async/await instead of raw Promises:

```typescript
// Good
async function fetchUser(id: string) {
  try {
    const user = await database.getUser(id);
    return user;
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
}
```

## Error Handling

- Use typed error handling:

```typescript
try {
  // Code that might throw
} catch (error) {
  if (error instanceof DatabaseError) {
    // Handle database errors
  } else if (error instanceof AuthError) {
    // Handle auth errors
  } else {
    // Handle unknown errors
  }
}
```

## Import/Export

- Use named exports instead of default exports for better refactoring:

```typescript
// In file.ts
export function doSomething() {/* */}

// In another file
import { doSomething } from './file';
```

- Group related functionality in namespaces or modules:

```typescript
export namespace Auth {
  export function verifyToken(token: string) {/* */}
  export function generateToken(payload: TokenPayload) {/* */}
}
``` 