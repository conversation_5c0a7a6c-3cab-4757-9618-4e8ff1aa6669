---
description: 
globs: 
alwaysApply: true
---
# Zod Best Practices

## Schema Definition

- Define Zod schemas in dedicated files or alongside related types:

```typescript
import { z } from 'zod';

// Define schema
export const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.string().min(2),
  role: z.enum(['user', 'admin']),
  metadata: z.record(z.string()).optional(),
});

// Export type derived from schema
export type User = z.infer<typeof UserSchema>;
```

## Request Validation

- Validate API request payloads using Zod:

```typescript
import { UserSchema } from './schemas';

export const createUser = async (c) => {
  try {
    // Validate request body
    const body = await c.req.json();
    const result = UserSchema.safeParse(body);
    
    if (!result.success) {
      return c.json({
        success: false,
        error: {
          message: 'Invalid request data',
          details: result.error.format()
        }
      }, 400);
    }
    
    const validatedData = result.data;
    // Process validated data
    
  } catch (error) {
    // Handle error
  }
};
```

## Response Validation

- Define response schemas and validate outgoing data:

```typescript
const UserResponseSchema = z.object({
  success: z.boolean(),
  data: UserSchema,
});

// Validate response before sending
const responseData = {
  success: true,
  data: user,
};

const result = UserResponseSchema.safeParse(responseData);
if (!result.success) {
  console.error('Invalid response data:', result.error);
  return c.json({
    success: false,
    error: { message: 'Internal server error' }
  }, 500);
}

return c.json(responseData);
```

## Schema Composition

- Compose schemas for reusability:

```typescript
// Base schema
const BaseUserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
});

// Extended schema
const UserWithProfileSchema = BaseUserSchema.extend({
  profile: z.object({
    name: z.string(),
    avatar: z.string().url().optional(),
  }),
});

// Partial schema for updates
const UserUpdateSchema = BaseUserSchema.partial();
```

## Custom Validation

- Add custom validators for complex rules:

```typescript
const TaskSchema = z.object({
  id: z.string(),
  status: z.enum(['WAITING', 'PROCESSING', 'SUCCESS', 'FINISHED', 'FAILED', 'CANCEL']),
  deadlineTime: z.string().datetime(),
}).refine(
  (data) => {
    // Custom validation logic
    if (data.status === 'WAITING' && new Date(data.deadlineTime) < new Date()) {
      return false;
    }
    return true;
  },
  {
    message: 'Deadline cannot be in the past for waiting tasks',
    path: ['deadlineTime'],
  }
);
```

## Error Handling

- Use structured error responses:

```typescript
try {
  const result = schema.safeParse(data);
  if (!result.success) {
    return {
      success: false,
      error: {
        message: 'Validation error',
        details: result.error.format(),
      }
    };
  }
  // Process valid data
} catch (error) {
  return {
    success: false,
    error: {
      message: 'Unexpected error during validation',
    }
  };
}
```

## Environment Variable Validation

- Validate environment variables at startup:

```typescript
const EnvSchema = z.object({
  JWT_SECRET: z.string().min(32),
  SUPABASE_URL: z.string().url(),
  SUPABASE_SERVICE_ROLE_KEY: z.string(),
});

// Validate environment
function validateEnv(env: Record<string, string>) {
  const result = EnvSchema.safeParse(env);
  if (!result.success) {
    console.error('Invalid environment configuration:', result.error.format());
    throw new Error('Invalid environment configuration');
  }
  return result.data;
}

// Usage
const validatedEnv = validateEnv(env);
``` 