---
description: 
globs: 
alwaysApply: true
---
# Supabase Best Practices

## Setup and Connection

- Initialize Supabase client with proper configuration:

```typescript
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  env.SUPABASE_URL,
  env.SUPABASE_SERVICE_ROLE_KEY
);
```

- Use environment variables for Supabase credentials:
  - `SUPABASE_URL`: Your Supabase project URL
  - `SUPABASE_SERVICE_ROLE_KEY`: Service role key for admin operations
  - `SUPABASE_ANON_KEY`: Anonymous key for client-side operations

## Authentication

- Use JWT tokens for authentication between services:

```typescript
// Verify JWT token
const { data, error } = await supabase.auth.getUser(token);
```

- Store API keys securely in Supabase tables with proper RLS policies

## Database Operations

- Use typed queries with TypeScript for better type safety:

```typescript
interface User {
  id: string;
  email: string;
  created_at: string;
}

const { data, error } = await supabase
  .from<User>('users')
  .select('*')
  .eq('id', userId);
```

- Implement proper error handling:

```typescript
const { data, error } = await supabase
  .from('users')
  .select('*')
  .eq('id', userId);

if (error) {
  console.error('Database error:', error);
  throw new Error('Failed to fetch user data');
}

return data;
```

## Transactions

- Use transactions for operations that need to be atomic:

```typescript
const { data, error } = await supabase.rpc('create_user_with_profile', {
  user_email: email,
  user_name: name
});
```

## Security

- Implement Row Level Security (RLS) policies for all tables:
  - Restrict access based on user ID
  - Use service role only for admin operations
  - Avoid using service role in client-facing code

- Example RLS policy:

```sql
CREATE POLICY "Users can only access their own data"
ON "public"."user_data"
FOR ALL
USING (auth.uid() = user_id);
```

## Performance

- Use selective column queries to reduce data transfer:

```typescript
const { data } = await supabase
  .from('users')
  .select('id, name, email')  // Only select needed columns
  .eq('id', userId);
```

- Use pagination for large result sets:

```typescript
const { data } = await supabase
  .from('tasks')
  .select('*')
  .range(0, 9);  // First 10 results
```

- Utilize indexes for frequently queried columns:

```sql
CREATE INDEX idx_task_user_id ON tasks(user_id);
``` 