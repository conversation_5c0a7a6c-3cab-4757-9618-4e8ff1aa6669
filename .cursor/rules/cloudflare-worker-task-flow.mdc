---
description: 
globs: 
alwaysApply: true
---
# Cloudflare Worker Task Flow

This rule documents the task flow system used in the API service.

## Task State Machine

Tasks in the system follow this state flow:

```mermaid
stateDiagram-v2
    [*] --> WAITING: Task Created
    WAITING --> PROCESSING: Start Processing
    PROCESSING --> SUCCESS: Task Completed Successfully
    PROCESSING --> FINISHED: Task Completed
    PROCESSING --> FAILED: Task Failed
    WAITING --> FAILED: Task Failed
    WAITING --> CANCEL: User Cancelled
    PROCESSING --> CANCEL: User Cancelled
    SUCCESS --> [*]
    FINISHED --> [*]
    FAILED --> [*]
    CANCEL --> [*]
```

## Task States

- **WAITING**
  - Initial state when task is created
  - Credits are pre-deducted
  - Task is queued for processing

- **PROCESSING**
  - Task is being processed by external services
  - System periodically checks task status
  - Can transition to SUCCESS, FINISHED, FAILED, or CANCEL

- **SUCCESS**
  - Task completed successfully
  - Final state
  - Credit deduction is confirmed

- **FINISHED**
  - Task completed (alternative success state)
  - Final state
  - Credit deduction is confirmed

- **FAILED**
  - Task processing failed
  - Final state
  - Credits are refunded to user
  - Error message is stored

- **CANCEL**
  - Task cancelled by user
  - Final state
  - Credits are refunded to user

## Implementation

Tasks are implemented using a combination of:

1. **Queue**: For processing background tasks
   - Initial task enqueuing in `queue.ts`
   - Queue message processing

2. **Durable Objects**: For stateful task information
   - Storing task state
   - Updating task progress

3. **KV Storage**: For task data persistence
   - Storing task results
   - Caching task information

4. **Database**: For permanent task records
   - Task history
   - Credit transactions

## Code Flow

1. **Task Creation**:
   ```typescript
   // Create task and save to database
   const task = await createTask(taskData);
   
   // Pre-deduct credits
   await deductCredits(userId, creditAmount);
   
   // Queue task for processing
   await env.TASK_QUEUE.send({ taskId: task.id });
   ```

2. **Queue Processing**:
   ```typescript
   // In queue.ts
   export async function handleTaskQueue(batch, env) {
     for (const message of batch.messages) {
       const { taskId } = message.body;
       // Process task
       await processTask(taskId, env);
     }
   }
   ```

3. **Task Updates**:
   ```typescript
   // Update task status
   export async function updateTaskStatus(taskId, status, progress) {
     // Update in database
     await supabase
       .from('tasks')
       .update({ status, progress })
       .eq('id', taskId);
     
     // Update in KV for fast access
     await env.TASK_KV.put(`task:${taskId}`, JSON.stringify({ status, progress }));
   }
   ```

4. **Task Completion**:
   ```typescript
   // Mark task as complete
   if (isSuccessful) {
     await updateTaskStatus(taskId, 'SUCCESS', 100);
     // Confirm credit deduction
   } else {
     await updateTaskStatus(taskId, 'FAILED', 0);
     // Refund credits
     await refundCredits(userId, creditAmount);
   }
   ```

## Error Handling

- Tasks that fail are moved to FAILED state
- Credits are automatically refunded for failed tasks
- Tasks that timeout are handled by scheduled functions in `scheduled.ts`
